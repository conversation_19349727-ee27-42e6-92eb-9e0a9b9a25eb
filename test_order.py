#!/usr/bin/env python3
"""
Simple WebSocket client to test order placement
"""
import asyncio
import websockets
import json
import time

async def test_order():
    uri = "ws://127.0.0.1:8080"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("Connected to WebSocket server")
            
            # 发送一个买单
            order_request = {
                "id": 1,
                "method": "order.place",
                "params": {
                    "symbol": "BTCUSDT",
                    "side": "BUY",
                    "type": "MARKET",
                    "quantity": "0.001"
                }
            }
            
            print(f"Sending order: {json.dumps(order_request, indent=2)}")
            await websocket.send(json.dumps(order_request))
            
            # 等待响应
            response = await websocket.recv()
            print(f"Received response: {response}")
            
            # 等待一下，看看是否有其他消息
            await asyncio.sleep(2)
            
            # 发送另一个卖单
            order_request2 = {
                "id": 2,
                "method": "order.place",
                "params": {
                    "symbol": "BTCUSDT",
                    "side": "SELL",
                    "type": "MARKET",
                    "quantity": "0.0005"
                }
            }
            
            print(f"Sending second order: {json.dumps(order_request2, indent=2)}")
            await websocket.send(json.dumps(order_request2))
            
            # 等待响应
            response2 = await websocket.recv()
            print(f"Received second response: {response2}")
            
            await asyncio.sleep(1)
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_order())
