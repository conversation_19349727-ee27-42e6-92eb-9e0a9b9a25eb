use crate::indicators::types::IndicatorResult;
use crate::types::{MarketData, Order, Trade};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 消息ID类型
pub type MessageId = Uuid;

/// 消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageType {
    /// 市场数据
    MarketData(MarketData),
    /// 订单
    Order(Order),
    /// 交易
    Trade(Trade),
    /// 技术指标结果
    Indicator(IndicatorResult),
    /// 系统控制消息
    Control(ControlMessage),
    /// 状态更新
    Status(StatusMessage),
}

/// 控制消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ControlMessage {
    /// 启动模块
    Start { module: String },
    /// 停止模块
    Stop { module: String },
    /// 重启模块
    Restart { module: String },
    /// 配置更新
    ConfigUpdate {
        module: String,
        config: serde_json::Value,
    },
    /// 健康检查
    HealthCheck { module: String },
    /// 关闭系统
    Shutdown,
}

/// 状态消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StatusMessage {
    /// 模块启动
    ModuleStarted { module: String },
    /// 模块停止
    ModuleStopped { module: String },
    /// 模块错误
    ModuleError { module: String, error: String },
    /// 健康状态
    Health { module: String, healthy: bool },
    /// 统计信息
    Stats {
        module: String,
        stats: serde_json::Value,
    },
}

/// 消息优先级
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum Priority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

/// 通用消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    /// 消息ID
    pub id: MessageId,
    /// 消息类型
    pub message_type: MessageType,
    /// 发送者
    pub sender: String,
    /// 接收者（None表示广播）
    pub receiver: Option<String>,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 优先级
    pub priority: Priority,
    /// 是否需要确认
    pub requires_ack: bool,
    /// 过期时间
    pub expires_at: Option<DateTime<Utc>>,
}

impl Message {
    /// 创建新消息
    pub fn new(message_type: MessageType, sender: String, receiver: Option<String>) -> Self {
        Self {
            id: Uuid::new_v4(),
            message_type,
            sender,
            receiver,
            timestamp: Utc::now(),
            priority: Priority::Normal,
            requires_ack: false,
            expires_at: None,
        }
    }

    /// 创建广播消息
    pub fn broadcast(message_type: MessageType, sender: String) -> Self {
        Self::new(message_type, sender, None)
    }

    /// 创建点对点消息
    pub fn to(message_type: MessageType, sender: String, receiver: String) -> Self {
        Self::new(message_type, sender, Some(receiver))
    }

    /// 设置优先级
    pub fn with_priority(mut self, priority: Priority) -> Self {
        self.priority = priority;
        self
    }

    /// 设置需要确认
    pub fn with_ack(mut self) -> Self {
        self.requires_ack = true;
        self
    }

    /// 设置过期时间
    pub fn with_expiry(mut self, expires_at: DateTime<Utc>) -> Self {
        self.expires_at = Some(expires_at);
        self
    }

    /// 检查消息是否过期
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            Utc::now() > expires_at
        } else {
            false
        }
    }

    /// 检查是否为广播消息
    pub fn is_broadcast(&self) -> bool {
        self.receiver.is_none()
    }

    /// 检查是否为特定接收者的消息
    pub fn is_for(&self, receiver: &str) -> bool {
        match &self.receiver {
            Some(r) => r == receiver,
            None => true, // 广播消息对所有人可见
        }
    }

    /// 获取消息大小（估算）
    pub fn estimated_size(&self) -> usize {
        // 简单的大小估算
        std::mem::size_of::<Self>()
            + self.sender.len()
            + self.receiver.as_ref().map_or(0, |r| r.len())
    }
}

impl Default for Priority {
    fn default() -> Self {
        Priority::Normal
    }
}

/// 消息确认
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageAck {
    pub message_id: MessageId,
    pub receiver: String,
    pub timestamp: DateTime<Utc>,
    pub success: bool,
    pub error: Option<String>,
}

impl MessageAck {
    /// 创建成功确认
    pub fn success(message_id: MessageId, receiver: String) -> Self {
        Self {
            message_id,
            receiver,
            timestamp: Utc::now(),
            success: true,
            error: None,
        }
    }

    /// 创建错误确认
    pub fn error(message_id: MessageId, receiver: String, error: String) -> Self {
        Self {
            message_id,
            receiver,
            timestamp: Utc::now(),
            success: false,
            error: Some(error),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{OrderSide, Price, Trade};

    #[test]
    fn test_message_creation() {
        let trade = Trade {
            id: "test1".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(100.0),
            quantity: 10.0,
            side: OrderSide::Buy,
            timestamp: Some(Utc::now()),
        };

        let message = Message::broadcast(MessageType::Trade(trade), "matching_engine".to_string());

        assert!(message.is_broadcast());
        assert_eq!(message.sender, "matching_engine");
        assert_eq!(message.priority, Priority::Normal);
        assert!(!message.requires_ack);
    }

    #[test]
    fn test_message_targeting() {
        let message = Message::to(
            MessageType::Control(ControlMessage::HealthCheck {
                module: "test".to_string(),
            }),
            "system".to_string(),
            "data_reader".to_string(),
        );

        assert!(!message.is_broadcast());
        assert!(message.is_for("data_reader"));
        assert!(!message.is_for("other_module"));
    }

    #[test]
    fn test_message_expiry() {
        let past_time = Utc::now() - chrono::Duration::seconds(10);
        let future_time = Utc::now() + chrono::Duration::seconds(10);

        let expired_message = Message::broadcast(
            MessageType::Control(ControlMessage::Shutdown),
            "system".to_string(),
        )
        .with_expiry(past_time);

        let valid_message = Message::broadcast(
            MessageType::Control(ControlMessage::Shutdown),
            "system".to_string(),
        )
        .with_expiry(future_time);

        assert!(expired_message.is_expired());
        assert!(!valid_message.is_expired());
    }
}
