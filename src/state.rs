use crate::account::AccountManager;
use crate::data::DataStreamController;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};

/// 全局应用状态
pub struct AppState {
    /// 数据流控制器
    pub data_stream_controller: Option<Arc<Mutex<DataStreamController>>>,
    /// 账户管理器
    pub account_manager: Option<Arc<Mutex<AccountManager>>>,
}

impl AppState {
    /// 创建新的应用状态
    pub fn new() -> Self {
        Self {
            data_stream_controller: None,
            account_manager: None,
        }
    }

    /// 设置数据流控制器
    pub fn set_data_stream_controller(&mut self, controller: Arc<Mutex<DataStreamController>>) {
        self.data_stream_controller = Some(controller);
    }

    /// 获取数据流控制器
    pub fn data_stream_controller(&self) -> Option<Arc<Mutex<DataStreamController>>> {
        self.data_stream_controller.clone()
    }

    /// 设置账户管理器
    pub fn set_account_manager(&mut self, manager: Arc<Mutex<AccountManager>>) {
        self.account_manager = Some(manager);
    }

    /// 获取账户管理器
    pub fn account_manager(&self) -> Option<Arc<Mutex<AccountManager>>> {
        self.account_manager.clone()
    }
}

/// 全局应用状态实例
static APP_STATE: RwLock<AppState> = RwLock::const_new(AppState {
    data_stream_controller: None,
    account_manager: None,
});

/// 获取全局应用状态
pub async fn get_app_state() -> tokio::sync::RwLockReadGuard<'static, AppState> {
    APP_STATE.read().await
}

/// 获取可变的全局应用状态
pub async fn get_app_state_mut() -> tokio::sync::RwLockWriteGuard<'static, AppState> {
    APP_STATE.write().await
}

/// 设置数据流控制器到全局状态
pub async fn set_data_stream_controller(controller: Arc<Mutex<DataStreamController>>) {
    let mut state = APP_STATE.write().await;
    state.set_data_stream_controller(controller);
}

/// 获取数据流控制器
pub async fn get_data_stream_controller() -> Option<Arc<Mutex<DataStreamController>>> {
    let state = APP_STATE.read().await;
    state.data_stream_controller()
}

/// 设置账户管理器到全局状态
pub async fn set_account_manager(manager: Arc<Mutex<AccountManager>>) {
    let mut state = APP_STATE.write().await;
    state.set_account_manager(manager);
}

/// 获取账户管理器
pub async fn get_account_manager() -> Option<Arc<Mutex<AccountManager>>> {
    let state = APP_STATE.read().await;
    state.account_manager()
}
